import React from 'react';
import { useNavigate } from 'react-router-dom';
import ModernLayout from '../../components/Layout/ModernLayout';
import AddUserForm from '../../components/Admin/AddUserForm';
import { FiUserPlus, FiArrowLeft } from 'react-icons/fi';

const AddUser = () => {
  const navigate = useNavigate();

  const handleUserAdded = () => {
    // Navigate back to users list after successful addition
    navigate('/users');
  };

  const handleCancel = () => {
    // Navigate back to users list when cancel is clicked
    navigate('/users');
  };

  return (
    <ModernLayout>
      <div className="space-y-4">
        {/* Page Header */}
        <div className="bg-white p-5 border-b border-gray-300 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-xl font-medium text-gray-800">Ajouter un Utilisateur</h2>
              <p className="text-sm text-gray-500 mt-1">Créer un nouveau compte utilisateur</p>
            </div>
            <div className="mt-4 md:mt-0">
              <button
                onClick={handleCancel}
                className="inline-flex items-center text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors duration-200"
              >
                <FiArrowLeft className="mr-2 h-4 w-4" />
                Retour à la liste
              </button>
            </div>
          </div>
        </div>

        {/* Add User Form */}
        <div className="max-w-2xl mx-auto">
          <AddUserForm onUserAdded={handleUserAdded} isStandalone={true} />
        </div>
      </div>
    </ModernLayout>
  );
};

export default AddUser;