import { useState, useEffect } from 'react';
import { useNavi<PERSON>, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import api from '../../services/api';
import ModernLayout from '../../components/Layout/ModernLayout';
import {
  FiFileText,
  FiUserCheck,
  FiArrowLeft,
  FiAlertCircle,
  FiUsers,
  FiSearch,
  FiMapPin,
  FiLayers,
  FiEdit,
  FiCheckCircle,
  FiTrash2,
  FiRefreshCw
} from 'react-icons/fi';
import Select from 'react-select';

const AssignReadingSheets = () => {
  const [readingSheets, setReadingSheets] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [tournees, setTournees] = useState([]);
  const [circuits, setCircuits] = useState([]);
  const [circuitData, setCircuitData] = useState({
    tourneesByGroup: {},
    circuitsByGroupAndTournee: {}
  });

  // Form state
  const [selectedGroup, setSelectedGroup] = useState('');
  const [selectedTournee, setSelectedTournee] = useState('');
  const [selectedCircuit, setSelectedCircuit] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');

  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);
  const [editingSheet, setEditingSheet] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch both assigned and completed reading sheets for display
        const sheetsRes = await api.get('/api/reading-sheets');
        const relevantSheets = sheetsRes.data.data.filter(
          sheet => sheet.status === 'assigned' || sheet.status === 'completed'
        );
        setReadingSheets(relevantSheets);

        try {
          // Fetch all circuit data in one request
          console.log('Fetching all circuit data...');
          const circuitDataRes = await api.get('/api/circuits/all-data');
          console.log('Circuit data response:', circuitDataRes.data);

          if (circuitDataRes.data.success) {
            const { groups, tourneesByGroup, circuitsByGroupAndTournee } = circuitDataRes.data.data;

            console.log('Groups:', groups);
            console.log('TourneesByGroup:', tourneesByGroup);
            console.log('CircuitsByGroupAndTournee:', circuitsByGroupAndTournee);

            setGroups(groups || []);
            setCircuitData({
              tourneesByGroup: tourneesByGroup || {},
              circuitsByGroupAndTournee: circuitsByGroupAndTournee || {}
            });
          } else {
            console.error('Circuit data response not successful:', circuitDataRes.data);
            toast.error('Échec du chargement des données de circuit');

            // Fallback to individual requests
            console.log('Falling back to individual requests...');
            const groupsRes = await api.get('/api/circuits/groups');
            console.log('Groups response:', groupsRes.data);
            setGroups(groupsRes.data.data || []);
          }
        } catch (circuitError) {
          console.error('Failed to fetch circuit data:', circuitError);
          console.error('Circuit error details:', circuitError.response?.data);
          toast.error('Échec du chargement des données de circuit');

          // Fallback to individual requests
          console.log('Falling back to individual requests after error...');
          try {
            const groupsRes = await api.get('/api/circuits/groups');
            console.log('Groups response:', groupsRes.data);
            setGroups(groupsRes.data.data || []);
          } catch (fallbackError) {
            console.error('Fallback request also failed:', fallbackError);
          }
        }

        try {
          // Fetch data entry users
          const usersRes = await api.get('/api/users');
          const dataEntryUsers = usersRes.data.data.filter(
            user => user.role === 'dataEntry' && user.active
          );
          setUsers(dataEntryUsers);
          setFilteredUsers(dataEntryUsers);
        } catch (userError) {
          console.error('Failed to fetch users:', userError);
          toast.error('Échec du chargement des utilisateurs. Vérifiez vos permissions.');
          setUsers([]);
          setFilteredUsers([]);
        }

        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch data:', error);
        console.error('Error details:', error.response?.data);
        toast.error('Échec du chargement des données');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Prepare options for react-select
  const groupOptions = groups.map(group => ({ value: group, label: `Groupe ${group}` }));
  const tourneeOptions = tournees.map(tournee => ({ value: tournee, label: `Tournée ${tournee}` }));
  const circuitOptions = circuits.map(circuit => ({ value: circuit._id, label: `Circuit ${circuit.circuit_number}` }));
  const userOptions = filteredUsers.map(user => ({ value: user._id, label: `${user.firstName} ${user.lastName}${user.reference ? ` (Réf: ${user.reference})` : ''}` }));

  // Handle group selection
  const handleGroupChange = async (groupValue) => {
    // Convert to number since groupe is stored as a number in the database
    const numericGroupValue = groupValue ? Number(groupValue) : '';
    console.log('Group selected:', groupValue, 'Converted to:', numericGroupValue);

    setSelectedGroup(numericGroupValue);
    setSelectedTournee('');
    setSelectedCircuit('');
    setCircuits([]);

    if (!numericGroupValue) {
      setTournees([]);
      return;
    }

    try {
      // First try to use cached data from the all-data endpoint
      const groupTournees = circuitData.tourneesByGroup[numericGroupValue] || [];
      console.log('Setting tournees for group from cache:', numericGroupValue, groupTournees);
      console.log('Available tournees by group keys:', Object.keys(circuitData.tourneesByGroup));

      if (groupTournees && groupTournees.length > 0) {
        setTournees(groupTournees);
      } else {
        // Fallback to API call if cache is empty
        console.log('Cache empty, fetching tournees for group from API:', numericGroupValue);
        const tourneesRes = await api.get(`/api/circuits/tournees/${numericGroupValue}`);
        console.log('Tournees API response:', tourneesRes.data);
        setTournees(tourneesRes.data.data || []);
      }
    } catch (error) {
      console.error('Failed to get tournees for group:', error);
      toast.error('Échec du chargement des tournées');

      // Try API call as fallback
      try {
        console.log('Trying API call as fallback for tournees');
        const tourneesRes = await api.get(`/api/circuits/tournees/${numericGroupValue}`);
        setTournees(tourneesRes.data.data || []);
      } catch (fallbackError) {
        console.error('Fallback API call also failed:', fallbackError);
        setTournees([]);
      }
    }
  };

  // Handle tournee selection
  const handleTourneeChange = async (tourneeValue) => {
    // Convert to number since tournee is stored as a number in the database
    const numericTourneeValue = tourneeValue ? Number(tourneeValue) : '';
    console.log('Tournee selected:', tourneeValue, 'Converted to:', numericTourneeValue);

    setSelectedTournee(numericTourneeValue);
    setSelectedCircuit('');
    setCircuits([]);

    if (!numericTourneeValue || !selectedGroup) {
      return;
    }

    try {
      // First try to use cached data
      console.log('Looking for circuits in cache for group:', selectedGroup, 'tournee:', numericTourneeValue);
      console.log('Available circuit groups:', Object.keys(circuitData.circuitsByGroupAndTournee));

      if (circuitData.circuitsByGroupAndTournee[selectedGroup]) {
        console.log('Available tournees for group:', Object.keys(circuitData.circuitsByGroupAndTournee[selectedGroup]));
      }

      const groupCircuits = circuitData.circuitsByGroupAndTournee[selectedGroup]?.[numericTourneeValue] || [];
      console.log('Found circuits in cache:', groupCircuits.length);

      if (groupCircuits && groupCircuits.length > 0) {
        setCircuits(groupCircuits);
      } else {
        // Fallback to API call if cache is empty
        console.log('Cache empty, fetching circuits from API for group:', selectedGroup, 'tournee:', numericTourneeValue);
        const circuitsRes = await api.get(`/api/circuits/circuits/${selectedGroup}/${numericTourneeValue}`);
        console.log('Circuits API response:', circuitsRes.data);
        setCircuits(circuitsRes.data.data || []);
      }
    } catch (error) {
      console.error('Failed to get circuits:', error);
      toast.error('Échec du chargement des circuits');

      // Try API call as fallback
      try {
        console.log('Trying API call as fallback for circuits');
        const circuitsRes = await api.get(`/api/circuits/circuits/${selectedGroup}/${numericTourneeValue}`);
        setCircuits(circuitsRes.data.data || []);
      } catch (fallbackError) {
        console.error('Fallback API call also failed:', fallbackError);
        setCircuits([]);
      }
    }
  };

  // Handle user search
  const handleUserSearch = (searchTerm) => {
    setUserSearchTerm(searchTerm);
    if (searchTerm.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user =>
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.reference && user.reference.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    if (!selectedGroup || !selectedTournee || !selectedCircuit || !selectedUser) {
      toast.error('Veuillez sélectionner tous les champs requis');
      return;
    }

    setAssigning(true);

    try {
      // Find the selected circuit details
      const selectedCircuitData = circuits.find(c => c._id === selectedCircuit);

      // Generate a unique sheet number
      const sheetNumber = `G${selectedGroup}-T${selectedTournee}-C${selectedCircuitData?.circuit_number}-${Date.now()}`;

      // Create and assign reading sheet
      const readingSheetData = {
        sheetNumber,
        area: `Groupe ${selectedGroup} - Tournée ${selectedTournee}`,
        circuit: selectedCircuit,
        assignedTo: selectedUser,
        status: 'assigned',
        assignedDate: new Date()
      };

      await api.post('/api/reading-sheets', readingSheetData);

      toast.success('Fiche de relevé créée et assignée avec succès');

      // Reset form
      setSelectedGroup('');
      setSelectedTournee('');
      setSelectedCircuit('');
      setSelectedUser('');
      setUserSearchTerm('');
      setTournees([]);
      setCircuits([]);
      setFilteredUsers(users);

      // Refresh reading sheets
      const sheetsRes = await api.get('/api/reading-sheets');
      const relevantSheets = sheetsRes.data.data.filter(
        sheet => sheet.status === 'assigned' || sheet.status === 'completed'
      );
      setReadingSheets(relevantSheets);

    } catch (error) {
      toast.error('Échec de la création de la fiche de relevé');
      console.error('Error creating reading sheet:', error);
    } finally {
      setAssigning(false);
    }
  };

  // Delete handler - removes the sheet from database
  const handleDelete = async (sheetId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette fiche ? Cette action est irréversible.')) return;
    try {
      await api.delete(`/api/reading-sheets/${sheetId}`);
      setReadingSheets(readingSheets.filter(sheet => sheet._id !== sheetId));
      toast.success('Fiche supprimée avec succès');
    } catch (error) {
      console.error('Error deleting sheet:', error);
      toast.error('Erreur lors de la suppression. Vérifiez vos permissions.');
    }
  };

  // Mark as completed handler
  const handleComplete = async (sheetId) => {
    if (!window.confirm('Confirmer la complétion de cette fiche ?')) return;
    try {
      const response = await api.put(`/api/reading-sheets/${sheetId}/complete`);

      // Update the reading sheet in the state instead of removing it
      setReadingSheets(readingSheets.map(sheet =>
        sheet._id === sheetId
          ? { ...sheet, status: 'completed', completedDate: new Date() }
          : sheet
      ));

      toast.success('Fiche marquée comme terminée');
    } catch (error) {
      console.error('Error completing reading sheet:', error);
      toast.error('Erreur lors de la complétion');
    }
  };

  // Edit handler (open modal)
  const handleEdit = (sheet) => {
    setEditingSheet(sheet);
    setShowEditModal(true);
  };

  // Reset handler - clears all completed reading sheets from the database
  const handleReset = async () => {
    // Count completed sheets
    const completedSheets = readingSheets.filter(sheet => sheet.status === 'completed');
    const completedCount = completedSheets.length;
    const totalCount = readingSheets.length;

    // Check if all sheets are completed
    const allCompleted = completedCount > 0 && completedCount === totalCount;

    // Prepare confirmation message based on completion status
    let confirmMessage = allCompleted
      ? 'Toutes les fiches sont terminées. Voulez-vous les réinitialiser pour commencer un nouveau cycle?'
      : `${completedCount} sur ${totalCount} fiches sont terminées. Voulez-vous quand même réinitialiser les fiches terminées?`;

    if (!window.confirm(confirmMessage)) return;

    if (completedCount === 0) {
      toast.info('Aucune fiche terminée à réinitialiser');
      return;
    }

    try {
      setLoading(true);

      // Get IDs of all completed sheets
      const completedSheetIds = completedSheets.map(sheet => sheet._id);

      // Delete all completed sheets
      const deletePromises = completedSheetIds.map(id =>
        api.delete(`/api/reading-sheets/${id}`)
      );

      await Promise.all(deletePromises);

      // Remove completed sheets from the local state
      const updatedSheets = readingSheets.filter(sheet => sheet.status !== 'completed');
      setReadingSheets(updatedSheets);

      toast.success(`${completedCount} fiches terminées ont été réinitialisées`);
    } catch (error) {
      console.error('Error resetting reading sheets:', error);
      toast.error('Erreur lors de la réinitialisation des fiches. Vérifiez vos permissions.');
    } finally {
      setLoading(false);
    }
  };

  // Validate all assigned sheets - mark all assigned sheets as completed
  const handleValidateAll = async () => {
    const assignedSheets = readingSheets.filter(sheet => sheet.status === 'assigned');
    const assignedCount = assignedSheets.length;

    if (assignedCount === 0) {
      toast.info('Aucune fiche assignée à valider');
      return;
    }

    const confirmMessage = `Voulez-vous marquer toutes les ${assignedCount} fiches assignées comme complétées?`;
    if (!window.confirm(confirmMessage)) return;

    try {
      setLoading(true);

      // Mark all assigned sheets as completed
      const completePromises = assignedSheets.map(sheet =>
        api.put(`/api/reading-sheets/${sheet._id}/complete`)
      );

      await Promise.all(completePromises);

      // Update the local state
      const updatedSheets = readingSheets.map(sheet =>
        sheet.status === 'assigned'
          ? { ...sheet, status: 'completed', completedDate: new Date() }
          : sheet
      );
      setReadingSheets(updatedSheets);

      toast.success(`${assignedCount} fiches ont été marquées comme complétées`);
    } catch (error) {
      console.error('Error validating all sheets:', error);
      toast.error('Erreur lors de la validation des fiches. Vérifiez vos permissions.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="flex items-center justify-center h-full min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="space-y-4">
     {/* Main Content */}
        <div className="space-y-6">
          {/* Selection Area */}
          <div className="bg-white border border-gray-200 shadow-sm p-6">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-2 flex items-center">
                <FiLayers className="mr-2 h-5 w-5 text-primary-600" />
                Sélection de Zone et Attribution
              </h3>

            </div>

            {users.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-yellow-50 rounded-full p-3 mb-4">
                  <FiAlertCircle className="h-8 w-8 text-yellow-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Aucun agent disponible</h3>
                <p className="text-gray-500 text-center max-w-md mb-4">
                  Il n'y a pas d'agents de saisie actifs disponibles pour l'assignation.
                </p>
                <Link
                  to="/reading-sheets"
                  className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                >
                  <FiFileText className="mr-2 h-4 w-4" />
                  Voir toutes les fiches
                </Link>
              </div>
            ) : (
              <form onSubmit={onSubmit} className="space-y-6">
                {/* Circuit Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="group" className="block text-sm font-medium text-gray-700 mb-1">
                      Groupe
                    </label>
                    <Select
                      inputId="group"
                      options={groupOptions}
                      value={groupOptions.find(opt => opt.value === selectedGroup) || null}
                      onChange={option => handleGroupChange(option ? option.value : '')}
                      placeholder="-- Sélectionner un groupe --"
                      isClearable
                      isSearchable
                    />
                  </div>
                  <div>
                    <label htmlFor="tournee" className="block text-sm font-medium text-gray-700 mb-1">
                      Tournée
                    </label>
                    <Select
                      inputId="tournee"
                      options={tourneeOptions}
                      value={tourneeOptions.find(opt => opt.value === selectedTournee) || null}
                      onChange={option => handleTourneeChange(option ? option.value : '')}
                      placeholder="-- Sélectionner une tournée --"
                      isClearable
                      isSearchable
                      isDisabled={!selectedGroup}
                    />
                  </div>
                  <div>
                    <label htmlFor="circuit" className="block text-sm font-medium text-gray-700 mb-1">
                      Circuit
                    </label>
                    <Select
                      inputId="circuit"
                      options={circuitOptions}
                      value={circuitOptions.find(opt => opt.value === selectedCircuit) || null}
                      onChange={option => setSelectedCircuit(option ? option.value : '')}
                      placeholder="-- Sélectionner un circuit --"
                      isClearable
                      isSearchable
                      isDisabled={!selectedTournee}
                    />
                  </div>
                </div>

                {/* User Selection */}
                <div>
                  <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 mb-1">
                    L'Attaché
                  </label>
                  <Select
                    inputId="userSearch"
                    options={userOptions}
                    value={userOptions.find(opt => opt.value === selectedUser) || null}
                    onChange={option => setSelectedUser(option ? option.value : '')}
                    placeholder="Rechercher par nom ou référence..."
                    isClearable
                    isSearchable
                  />
                </div>

                <div className="flex justify-end">
                  <Link
                    to="/"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 mr-3"
                  >
                    <FiArrowLeft className="mr-2 h-4 w-4" />
                    Retour au Tableau de Bord
                  </Link>
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                    disabled={assigning}
                  >
                    {assigning ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <FiUserCheck className="mr-2 h-4 w-4" /> Assigner Fiche de Relevé
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>

          {/* Reading Progress */}
          <div className="bg-white border border-gray-200 shadow-sm p-6">
            <div className="mb-4 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-800 mb-2 flex items-center">
                <FiMapPin className="mr-2 h-5 w-5 text-primary-600" />
                Progression des Relevés
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={handleValidateAll}
                  className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium hover:bg-green-700 transition-colors duration-200"
                  title="Marquer toutes les fiches assignées comme complétées"
                  disabled={loading || readingSheets.filter(sheet => sheet.status === 'assigned').length === 0}
                >
                  <FiCheckCircle className="mr-1.5 h-4 w-4" />
                  Valide Tout
                </button>
                <button
                  onClick={handleReset}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200"
                  title="Réinitialiser les fiches terminées"
                  disabled={loading}
                >
                  <FiRefreshCw className="mr-1.5 h-4 w-4" />
                  Réinitialiser
                </button>
              </div>
            </div>

            {readingSheets.length === 0 ? (
              <div className="text-center py-8">
                <div className="bg-gray-50 rounded-full p-3 mx-auto w-16 h-16 flex items-center justify-center mb-4">
                  <FiAlertCircle className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500">Aucune fiche de relevé assignée pour le moment.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Numéro de Fiche
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Circuit
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigné à
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date d'assignation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {readingSheets.map(sheet => (
                      <tr key={sheet._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {sheet.sheetNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.circuit ? `G${sheet.circuit.groupe} - T${sheet.circuit.tournee} - C${sheet.circuit.circuit_number}` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.assignedTo ? `${sheet.assignedTo.firstName} ${sheet.assignedTo.lastName}` : 'Non assigné'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.assignedDate ? new Date(sheet.assignedDate).toLocaleDateString('fr-FR') : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            sheet.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                            sheet.status === 'completed' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {sheet.status === 'assigned' ? 'Assigné' :
                             sheet.status === 'completed' ? 'Terminé' :
                             sheet.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          <div className="flex justify-end space-x-2">
                            <button
                              className="p-1.5 text-gray-600 hover:text-primary-600 hover:bg-gray-100 rounded"
                              title="Modifier"
                              onClick={() => handleEdit(sheet)}
                            >
                              <FiEdit className="h-4 w-4" />
                            </button>
                            <button
                              className="p-1.5 text-green-600 hover:text-green-700 hover:bg-green-50 rounded"
                              title="Terminer"
                              onClick={() => handleComplete(sheet._id)}
                            >
                              <FiCheckCircle className="h-4 w-4" />
                            </button>
                            <button
                              className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 rounded"
                              title="Supprimer"
                              onClick={() => handleDelete(sheet._id)}
                            >
                              <FiTrash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Edit Modal with all editable attributes */}
      {showEditModal && editingSheet && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg w-full max-w-md">
            <h2 className="text-lg font-bold mb-4 flex items-center">
              <FiEdit className="mr-2 h-5 w-5 text-primary-600" />
              Modifier la fiche de relevé
            </h2>
            {/* Group Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Groupe</label>
              <Select
                options={groupOptions}
                value={editingSheet.circuit && editingSheet.circuit.groupe ? { value: editingSheet.circuit.groupe, label: `Groupe ${editingSheet.circuit.groupe}` } : null}
                onChange={async (option) => {
                  const selectedGroupValue = option ? Number(option.value) : '';
                  const updatedSheet = {
                    ...editingSheet,
                    selectedGroup: selectedGroupValue,
                    selectedTournee: '',
                    circuit: null
                  };
                  setEditingSheet(updatedSheet);
                  if (selectedGroupValue) {
                    try {
                      const tourneesRes = await api.get(`/api/circuits/tournees/${selectedGroupValue}`);
                      const editTournees = tourneesRes.data.data || [];
                      setEditingSheet({
                        ...updatedSheet,
                        availableTournees: editTournees
                      });
                    } catch (error) {
                      console.error('Failed to fetch tournees:', error);
                    }
                  }
                }}
                placeholder="Sélectionner un groupe..."
                isClearable
              />
            </div>
            {/* Tournee Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Tournée</label>
              <Select
                options={editingSheet.availableTournees ? editingSheet.availableTournees.map(t => ({ value: t, label: `Tournée ${t}` })) : tourneeOptions}
                value={editingSheet.circuit && editingSheet.circuit.tournee ? { value: editingSheet.circuit.tournee, label: `Tournée ${editingSheet.circuit.tournee}` } : null}
                onChange={async (option) => {
                  const selectedTourneeValue = option ? Number(option.value) : '';
                  const updatedSheet = {
                    ...editingSheet,
                    selectedTournee: selectedTourneeValue,
                    circuit: null
                  };
                  setEditingSheet(updatedSheet);
                  if (selectedTourneeValue && editingSheet.selectedGroup) {
                    try {
                      const circuitsRes = await api.get(`/api/circuits/circuits/${editingSheet.selectedGroup}/${selectedTourneeValue}`);
                      const editCircuits = circuitsRes.data.data || [];
                      setEditingSheet({
                        ...updatedSheet,
                        availableCircuits: editCircuits
                      });
                    } catch (error) {
                      console.error('Failed to fetch circuits:', error);
                    }
                  }
                }}
                placeholder="Sélectionner une tournée..."
                isClearable
                isDisabled={!editingSheet.selectedGroup}
              />
            </div>
            {/* Circuit Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Circuit</label>
              <Select
                options={editingSheet.availableCircuits ? editingSheet.availableCircuits.map(c => ({ value: c._id, label: `Circuit ${c.circuit_number}` })) : circuitOptions}
                value={editingSheet.circuit && editingSheet.circuit._id ? { value: editingSheet.circuit._id, label: `Circuit ${editingSheet.circuit.circuit_number}` } : null}
                onChange={(option) => {
                  const selectedCircuitId = option ? option.value : null;
                  const selectedCircuit = editingSheet.availableCircuits ? editingSheet.availableCircuits.find(c => c._id === selectedCircuitId) : null;
                  setEditingSheet({
                    ...editingSheet,
                    circuit: selectedCircuit
                  });
                }}
                placeholder="Sélectionner un circuit..."
                isClearable
                isDisabled={!editingSheet.selectedTournee}
              />
            </div>
            {/* Assigned User */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Agent assigné</label>
              <Select
                options={userOptions}
                value={userOptions.find(opt => opt.value === (editingSheet.assignedTo?._id || editingSheet.assignedTo)) || null}
                onChange={option => setEditingSheet({ ...editingSheet, assignedTo: option ? option.value : '' })}
                placeholder="Sélectionner un agent..."
                isClearable
                isSearchable
              />
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <button
                className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                onClick={() => setShowEditModal(false)}
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
                onClick={async () => {
                  try {
                    if (!editingSheet.circuit || !editingSheet.assignedTo) {
                      toast.error('Veuillez sélectionner un circuit et un agent');
                      return;
                    }
                    // Generate updated area text
                    const areaText = `Groupe ${editingSheet.circuit.groupe} - Tournée ${editingSheet.circuit.tournee}`;
                    setReadingSheets(readingSheets.map(sheet =>
                      sheet._id === editingSheet._id
                        ? {
                            ...sheet,
                            circuit: editingSheet.circuit,
                            area: areaText,
                            assignedTo: typeof editingSheet.assignedTo === 'string'
                              ? users.find(u => u._id === editingSheet.assignedTo)
                              : editingSheet.assignedTo
                        }
                        : sheet
                    ));
                    toast.success('Fiche mise à jour');
                    setShowEditModal(false);
                  } catch (error) {
                    console.error('Error updating sheet:', error);
                    toast.error('Erreur lors de la mise à jour');
                  }
                }}
              >
                Enregistrer
              </button>
            </div>
          </div>
        </div>
      )}
    </ModernLayout>
  );
};

export default AssignReadingSheets;
