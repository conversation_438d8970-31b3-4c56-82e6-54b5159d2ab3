const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config/config');

const UserSchema = new mongoose.Schema({
  reference: {
    type: String,
    trim: true,
    unique: true
  },
  firstName: {
    type: String,
    required: [true, 'Please add a first name'],
    trim: true
  },
  lastName: {
    type: String,
    required: [true, 'Please add a last name'],
    trim: true
  },
  username: {
    type: String,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    // Email is now optional
    unique: true,
    sparse: true, // This allows multiple documents to have no email (null/undefined)
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ]
  },
  phoneNumber: {
    type: String,
    trim: true
  },
  address: {
    type: String,
    trim: true
  },
  password: {
    type: String,
    required: [true, 'Please add a password'],
    minlength: 5, // Changed from 6 to 5
    select: false
  },
  role: {
    type: String,
    enum: ['admin', 'dataEntry', 'salesRep'],
    default: 'dataEntry'
  },
  active: {
    type: Boolean,
    default: true
  },
  accountUsageStatus: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  inactiveDate: {
    type: Date
  }
});

// Generate username from first and last name and reference number
UserSchema.pre('save', async function(next) {
  try {
    // Only generate username if it's a new user or if first/last name changed
    if (this.isNew || this.isModified('firstName') || this.isModified('lastName')) {
      // Ensure firstName and lastName exist and are strings
      if (!this.firstName || !this.lastName) {
        console.log('Missing firstName or lastName:', { firstName: this.firstName, lastName: this.lastName });
        return next(new Error('First name and last name are required'));
      }

      // Simple approach: just use first and last name with basic cleaning
      const firstName = this.firstName.toString().trim().toLowerCase().replace(/\s+/g, '');
      const lastName = this.lastName.toString().trim().toLowerCase().replace(/\s+/g, '');

      // Create a simple username
      let baseUsername = `${lastName}.${firstName}`;

      // Remove any special characters except dots
      baseUsername = baseUsername.replace(/[^a-z0-9.]/g, '');

      // Ensure it's not empty
      if (!baseUsername || baseUsername === '.') {
        baseUsername = `user${Date.now()}`;
      }

      this.username = baseUsername;
      console.log('Generated base username:', this.username);

      // Check if username already exists
      try {
        const usernameExists = await this.constructor.findOne({
          username: this.username,
          _id: { $ne: this._id }
        });

        // If username exists, add a random number to make it unique
        if (usernameExists) {
          const randomNum = Math.floor(Math.random() * 1000);
          this.username = `${this.username}${randomNum}`;
          console.log('Username exists, using:', this.username);
        }
      } catch (dbError) {
        console.log('Error checking username uniqueness:', dbError);
        // If there's an error checking, just add a timestamp to be safe
        this.username = `${baseUsername}${Date.now()}`;
      }
    }

    // Generate reference number if it's a new user
    if (this.isNew && !this.reference) {
      // Format: 133 + role code (1 for admin, 2 for dataEntry, 3 for salesRep) + sequence (001-999)
      let roleCode;
      if (this.role === 'admin') {
        roleCode = '1';
      } else if (this.role === 'dataEntry') {
        roleCode = '2';
      } else if (this.role === 'salesRep') {
        roleCode = '3';
      } else {
        roleCode = '2'; // Default to dataEntry
      }

      try {
        // Find the highest sequence number for this role
        const highestRef = await this.constructor.findOne(
          { reference: { $regex: `^133${roleCode}` } },
          { reference: 1 },
          { sort: { reference: -1 } }
        );

        let sequence = 1;
        if (highestRef && highestRef.reference) {
          // Extract the sequence number from the reference
          const currentSequence = parseInt(highestRef.reference.substring(4), 10);
          if (!isNaN(currentSequence)) {
            sequence = currentSequence + 1;
          }
        }

        // Ensure sequence doesn't exceed 999
        if (sequence > 999) {
          return next(new Error('Maximum number of users for this role has been reached'));
        }

        // Format the sequence with leading zeros (001, 002, etc.)
        const formattedSequence = sequence.toString().padStart(3, '0');

        // Create the reference
        this.reference = `133${roleCode}${formattedSequence}`;
      } catch (refError) {
        console.error('Error generating reference:', refError);
        return next(new Error('Failed to generate user reference'));
      }
    }

    // If user is being deactivated, set inactiveDate
    if (this.isModified('active') && !this.active) {
      this.inactiveDate = new Date();
    }

    next();
  } catch (error) {
    console.error('Error in User pre-save hook:', error);
    next(error);
  }
});

// Encrypt password using bcrypt
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { id: this._id, role: this.role },
    config.JWT_SECRET,
    { expiresIn: config.JWT_EXPIRE }
  );
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

module.exports = mongoose.model('User', UserSchema);
