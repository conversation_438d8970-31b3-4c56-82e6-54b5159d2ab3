import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import SonelgazLogo from '../SonelgazLogo';
import {
  FiHome,
  FiUsers,
  FiUserPlus,
  FiFileText,
  FiClipboard,
  FiSettings,
  FiLogOut,
  FiMenu,
  FiX,
  FiChevronDown,
  FiUser,
  FiGrid,
  FiPower,
  FiShield,
  FiActivity,
  FiHelpCircle,
  FiSearch,
  FiDownload, // Use this instead of FiFileExport
  FiKey
} from 'react-icons/fi';

const ModernLayout = ({ children }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const dropdownRef = useRef(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [helpOpen, setHelpOpen] = useState(false);
  const [quickActionsOpen, setQuickActionsOpen] = useState(false);
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  const isAdmin = user?.role === 'admin';

  const adminMenuItems = [
    {
      path: '/users',
      icon: <FiUsers className="text-xl" />,
      label: 'Gérer Utilisateurs',
      description: 'Gestion des comptes utilisateurs'
    },
    {
      path: '/add-user',
      icon: <FiUserPlus className="text-xl" />,
      label: 'Ajouter Utilisateur',
      description: 'Création de nouveaux comptes'
    },
    {
      path: '/settings',
      icon: <FiSettings className="text-xl" />,
      label: 'Paramètres',
      description: 'Configuration du compte'
    },
  ];

  const dataEntryMenuItems = [
    {
      path: '/dashboard',
      icon: <FiGrid className="text-xl" />,
      label: 'Tableau de Bord',
      description: 'Résumé des opérations quotidiennes'
    },
    {
      path: '/reading-sheets/assign',
      icon: <FiClipboard className="text-xl" />,
      label: 'Assigner Fiches de Relevé',
      description: 'Assigner des fiches aux agents'
    },
    {
      path: '/reading-sheets/receive',
      icon: <FiFileText className="text-xl" />,
      label: 'Réceptionner Fiches',
      description: 'Réceptionner les fiches soumises'
    },
    {
      path: '/manage-readings',
      icon: <FiDownload className="text-xl" />,
      label: 'Export Relevés',
      description: 'Exporter tous les relevés sauvegardés'
    },
    {
      path: '/settings',
      icon: <FiSettings className="text-xl" />,
      label: 'Paramètres',
      description: 'Configuration du compte et mot de passe'
    }
  ];

  const menuItems = isAdmin ? adminMenuItems : dataEntryMenuItems;

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Handle click outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update date and time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 60000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      {/* Sidebar - Desktop */}
      <aside
        className={`fixed lg:relative h-full w-64 transform ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 transition-transform duration-300 ease-in-out z-30 flex flex-col shadow-md bg-white flex-shrink-0`}
        style={{ borderRight: '1px solid #e5e7eb', background: '#fff' }}
      >
        {/* Sidebar Header - Modern Sonelgaz Theme */}
        <div className="sidebar-header-sonelgaz bg-primary-600 flex flex-col items-center py-6">
          <div className="bg-white p-3 rounded-lg shadow-md">
            <SonelgazLogo width="60px" height="60px" variant="square" />
          </div>
          <h1 className="text-white font-bold text-mm mt-5 tracking-wide">SONELGAZ DISTRIBUTION</h1>
        </div>

        {/* Removed user info block here */}

        {/* Navigation Menu - Modern Theme */}
        <nav className="flex-1 px-3 py-4 bg-white overflow-y-auto">
          <div className="text-xs font-bold text-primary-600 uppercase tracking-wider mb-4 px-3">
            Menu
          </div>
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center gap-3 px-4 py-3 mb-1 transition-colors duration-200
                ${location.pathname === item.path
                  ? 'bg-primary-50 text-primary-700 font-medium border-l-4 border-primary-600'
                  : 'text-primary-700 hover:bg-primary-50 border-l-4 border-transparent'}
              `}
            >
              <div className="flex items-center justify-center">
                {item.icon}
              </div>
              <span className="block">{item.label}</span>
            </Link>
          ))}
        </nav>

        {/* Footer with Logout Button - Modern Theme */}
        <div className="p-4 bg-white border-t border-primary-100">
          <button
            onClick={handleLogout}
            className="flex items-center w-full bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 px-4 py-2 transition-colors duration-200"
          >
            <FiLogOut className="h-4 w-4 mr-2 text-gray-500" />
            <span className="font-medium">Déconnexion</span>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:ml-0 w-full">
        {/* Top Navigation - Formal Style */}
        <header className="z-20 sticky top-0">

          {/* Main header with navigation and actions */}
          <div className="bg-white shadow-sm border-b border-gray-200">
            <div className="px-4">
              <div className="flex items-center justify-between h-14">
                {/* Left side - Mobile menu toggle and page title */}
                <div className="flex items-center">
                  <button
                    onClick={toggleSidebar}
                    className="lg:hidden p-2 text-gray-600 focus:outline-none"
                  >
                    {sidebarOpen ? <FiX className="h-5 w-5" /> : <FiMenu className="h-5 w-5" />}
                  </button>

                  <div className="ml-2 sm:ml-4">
                    <h1 className="text-sm sm:text-base font-medium text-gray-800">
                      <span className="hidden sm:inline">
                        {currentDateTime.toLocaleDateString('fr-FR', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        }).replace(/^\w/, c => c.toUpperCase())} |
                      </span>
                      {currentDateTime.toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </h1>
                  </div>
                </div>

                {/* Right side - Header actions */}
                <div className="flex items-center">
                  {/* User Dropdown - Simplified */}
                  <div className="relative" ref={dropdownRef}>
                    <button
                      onClick={() => setUserMenuOpen(!userMenuOpen)}
                      className="flex items-center gap-2 p-2 hover:bg-gray-100 transition-colors duration-200"
                      title={user?.firstName || user?.lastName ? `${user?.firstName || ''}${user?.firstName && user?.lastName ? ' ' : ''}${user?.lastName || ''}`.trim() : 'Utilisateur'}
                    >
                       <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center text-white text-sm font-medium">
                        {(user?.firstName || user?.lastName)
                          ? `${user?.firstName?.charAt(0)?.toUpperCase() || ''}${user?.lastName?.charAt(0)?.toUpperCase() || ''}`
                          : <FiUser className="h-4 w-4" />}
                      </div>
                      <span className="hidden sm:inline text-sm font-medium text-gray-700 mr-1">
                        {(user?.firstName || user?.lastName)
                          ? `${user?.firstName || ''}${user?.firstName && user?.lastName ? ' ' : ''}${user?.lastName || ''}`.trim()
                          : 'Utilisateur'}
                      </span>

                      <FiChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${userMenuOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {userMenuOpen && (
                      <div className="absolute right-0 mt-1 w-48 shadow-md bg-white border border-gray-200 focus:outline-none z-50 overflow-hidden">
                        {/* Menu Items - Simplified */}
                        <div className="py-1">
                          <div className="px-4 py-2 border-b border-gray-100">
                            <div className="text-sm font-medium text-gray-800">{(user?.firstName || user?.lastName)
                              ? `${user?.firstName || ''}${user?.firstName && user?.lastName ? ' ' : ''}${user?.lastName || ''}`.trim()
                              : 'Utilisateur'}</div>
                            <div className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</div>
                          </div>

                          <Link
                            to="/settings"
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          >
                            <FiSettings className="h-4 w-4 mr-2 text-gray-500" />
                            Paramètres
                          </Link>

                          {isAdmin && (
                            <Link
                              to="/add-user"
                              className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiUserPlus className="h-4 w-4 mr-2 text-gray-500" />
                              Ajouter utilisateur
                            </Link>
                          )}

                          <button
                            onClick={handleLogout}
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-t border-gray-100"
                          >
                            <FiLogOut className="h-4 w-4 mr-2 text-gray-500" />
                            Déconnexion
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>



        {/* Mobile menu - Simplified */}
        {mobileMenuOpen && (
          <div className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-30">
            <div className="fixed inset-y-0 left-0 w-64 bg-white shadow-md">
              <div className="p-3 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center">
                  <SonelgazLogo width="32px" height="32px" variant="square" />
                  <h1 className="ml-2 text-base font-medium text-gray-800">SONELGAZ </h1>
                </div>
                <button onClick={toggleMobileMenu} className="text-gray-500">
                  <FiX className="h-5 w-5" />
                </button>
              </div>
              <div className="p-2">
                {menuItems.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center px-3 py-2 text-sm mb-1 ${
                      location.pathname === item.path
                        ? 'bg-gray-100 text-primary-700 font-medium border-l-2 border-primary-600'
                        : 'text-gray-700 hover:bg-gray-50 border-l-2 border-transparent'
                    }`}
                    onClick={toggleMobileMenu}
                  >
                    {item.icon}
                    <span className="ml-3">{item.label}</span>
                  </Link>
                ))}
              </div>
              <div className="p-3 mt-auto border-t border-gray-200">
                <button
                  onClick={() => {
                    toggleMobileMenu();
                    handleLogout();
                  }}
                  className="flex items-center w-full text-gray-700 hover:bg-gray-50 px-3 py-2 text-sm"
                >
                  <FiLogOut className="h-4 w-4 mr-3 text-gray-500" />
                  <span>Déconnexion</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Page content - Responsive */}
        <main className="flex-1 overflow-y-auto bg-gray-50 relative">
          {/* Content with responsive padding */}
          <div className="relative z-10 p-2 sm:p-4 lg:p-6 max-w-full">
            <div className="w-full max-w-none">
              {children}
            </div>
          </div>
        </main>
      </div> {/* Close .flex-1 flex flex-col ... */}
    </div> // Close .flex h-screen ...
  );
};

export default ModernLayout;