import { useState } from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import { FiUserPlus, FiX, FiUser } from 'react-icons/fi';

const AddUserForm = ({ onClose, onUserAdded, isStandalone = false }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: 'agent', // Default password
    role: 'dataEntry',
    phoneNumber: '',
    address: ''
    // reference is auto-generated
  });
  const [loading, setLoading] = useState(false);

  const { firstName, lastName, email, password, role, phoneNumber, address } = formData;

  const onChange = (e) => {
    const { name, value } = e.target;

    // Update form data
    setFormData({ ...formData, [name]: value });

    // If role changes, update password accordingly
    if (name === 'role') {
      const newPassword = value === 'dataEntry' ? 'agent' : 'attaché';
      setFormData(prevState => ({
        ...prevState,
        [name]: value,
        password: newPassword
      }));
    }
  };

  const handleCancel = () => {
    if (isStandalone) {
      navigate('/users');
    } else if (onClose) {
      onClose();
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Send user data with separate first and last name
      const userData = {
        firstName,
        lastName,
        email,
        password,
        role,
        phoneNumber,
        address
        // reference is auto-generated
      };

      await api.post('/api/users', userData);
      toast.success('Utilisateur ajouté avec succès');

      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: 'agent', // Default password
        role: 'dataEntry',
        phoneNumber: '',
        address: ''
        // reference is auto-generated
      });

      // Notify parent component
      if (onUserAdded) {
        onUserAdded();
      }

      // Close form if needed
      if (onClose) {
        onClose();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Échec de l\'ajout de l\'utilisateur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white ${!isStandalone ? 'border border-gray-200 shadow-sm' : ''} p-6`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-800">
          {isStandalone ? 'Informations de l\'utilisateur' : 'Ajouter un Utilisateur'}
        </h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FiX className="h-5 w-5" />
          </button>
        )}
      </div>

      <form onSubmit={onSubmit}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                Prénom
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={firstName}
                onChange={onChange}
                className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                required
                placeholder="Prénom"
              />
            </div>

            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                Nom
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={lastName}
                onChange={onChange}
                className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                required
                placeholder="Nom"
              />
            </div>
          </div>

          {/* Reference field removed as it's auto-generated */}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Adresse Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={onChange}
              className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Email (optionnel)"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Numéro de téléphone
              </label>
              <input
                type="text"
                id="phoneNumber"
                name="phoneNumber"
                value={phoneNumber}
                onChange={onChange}
                className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Téléphone (optionnel)"
              />
            </div>

            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Adresse
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={address}
                onChange={onChange}
                className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Adresse (optionnel)"
              />
            </div>
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
              Rôle
            </label>
            <select
              id="role"
              name="role"
              value={role}
              onChange={onChange}
              className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="dataEntry">Agent de Saisie</option>
              <option value="salesRep">Attaché Commercial</option>
            </select>
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Mot de Passe
            </label>
            <select
              id="password"
              name="password"
              value={password}
              onChange={onChange}
              className="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              required
            >
              <option value="agent">agent</option>
              <option value="attaché">attaché</option>
            </select>
            <p className="mt-1 text-xs text-gray-500">
              Le mot de passe est automatiquement mis à jour en fonction du rôle sélectionné
            </p>
          </div>

          <div className="pt-4">
            <button
              type="submit"
              className="w-full px-4 py-2 bg-primary-600 text-white font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center justify-center rounded-md transition-colors duration-200"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Création en cours...
                </span>
              ) : (
                <span className="flex items-center">
                  <FiUserPlus className="mr-2 h-4 w-4" />
                  {isStandalone ? 'Enregistrer l\'utilisateur' : 'Créer l\'utilisateur'}
                </span>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddUserForm;