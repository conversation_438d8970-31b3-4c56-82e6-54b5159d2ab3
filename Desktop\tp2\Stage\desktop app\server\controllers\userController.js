const User = require('../models/User');
const bcrypt = require('bcryptjs');

// @desc    Register a new user
// @route   POST /api/users
// @access  Private/Admin
exports.registerUser = async (req, res) => {
  try {
    const { firstName, lastName, email, password, role, phoneNumber, address, reference } = req.body;

    // Validate required fields
    if (!firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'Le prénom et le nom sont requis'
      });
    }

    // Check if user already exists (only if email is provided)
    if (email) {
      const userExists = await User.findOne({ email });

      if (userExists) {
        return res.status(400).json({
          success: false,
          message: 'Un utilisateur avec cet email existe déjà'
        });
      }
    }

    // Create user - username will be auto-generated in the pre-save hook
    const user = await User.create({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email ? email.trim() : undefined,
      password,
      role: role || 'dataEntry',
      phoneNumber: phoneNumber ? phoneNumber.trim() : undefined,
      address: address ? address.trim() : undefined,
      reference,
      active: true,
      accountUsageStatus: false
    });

    res.status(201).json({
      success: true,
      data: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
        email: user.email,
        role: user.role,
        active: user.active,
        phoneNumber: user.phoneNumber,
        address: user.address,
        reference: user.reference,
        accountUsageStatus: user.accountUsageStatus,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Error creating user:', error);

    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({
        success: false,
        message: `Un utilisateur avec ce ${field === 'email' ? 'email' : field === 'username' ? 'nom d\'utilisateur' : field} existe déjà`
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de l\'utilisateur',
      error: error.message
    });
  }
};

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
exports.getUsers = async (req, res) => {
  try {
    const users = await User.find();

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get user details
// @route   GET /api/users/:id/details
// @access  Private/Admin
exports.getUserDetails = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Return all user details
    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        reference: user.reference,
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
        email: user.email,
        phoneNumber: user.phoneNumber,
        address: user.address,
        role: user.role,
        active: user.active,
        accountUsageStatus: user.accountUsageStatus,
        createdAt: user.createdAt,
        inactiveDate: user.inactiveDate
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res) => {
  try {
    const { firstName, lastName, email, role, active, phoneNumber, address, reference, password } = req.body;

    console.log('Update request received:', {
      id: req.params.id,
      firstName,
      lastName,
      email,
      role,
      hasPassword: !!password
    });

    // Find user with password field included
    let user = await User.findById(req.params.id).select('+password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update fields
    user.firstName = firstName || user.firstName;
    user.lastName = lastName || user.lastName;
    user.email = email || user.email;
    user.role = role || user.role;
    user.active = active !== undefined ? active : user.active;
    user.phoneNumber = phoneNumber !== undefined ? phoneNumber : user.phoneNumber;
    user.address = address !== undefined ? address : user.address;
    // Reference cannot be modified as it's auto-generated

    // Update password if provided
    if (password) {
      console.log('Updating password for user:', user._id);
      user.password = password; // The pre-save hook in the User model will hash this
    }

    const updatedUser = await user.save();
    console.log('User updated successfully:', updatedUser._id);

    // Don't return the password in the response
    updatedUser.password = undefined;

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Search users
// @route   GET /api/users/search
// @access  Private/Admin
exports.searchUsers = async (req, res) => {
  try {
    const { firstName, lastName, username, email, role, reference } = req.query;
    const query = {};

    if (firstName) query.firstName = { $regex: firstName, $options: 'i' };
    if (lastName) query.lastName = { $regex: lastName, $options: 'i' };
    if (username) query.username = { $regex: username, $options: 'i' };
    if (email) query.email = { $regex: email, $options: 'i' };
    if (reference) query.reference = { $regex: reference, $options: 'i' };
    if (role) query.role = role;

    const users = await User.find(query);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent deleting admin users
    if (user.role === 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete admin users'
      });
    }

    // Prevent deleting users who have already used their account
    if (user.accountUsageStatus) {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete users who have already used their account'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
