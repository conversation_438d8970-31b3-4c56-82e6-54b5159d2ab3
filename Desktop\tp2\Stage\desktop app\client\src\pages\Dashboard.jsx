import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import ModernLayout from '../components/Layout/ModernLayout';
import bannerBg from '../assets/images/banner_sonelgaz.jpg';
import {
  FiFileText,
  FiCheckCircle,
  FiClock,
  FiCalendar,
  FiMoreVertical,
  FiSearch,
  FiFilter,
  FiAlertTriangle,
  FiUsers,
  FiUserPlus,
  FiUserCheck,
  FiActivity,
  FiBarChart2,
  FiPieChart,
  FiRefreshCw
} from 'react-icons/fi';

const Dashboard = () => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalSheets: 0,
    assignedSheets: 0,
    completedSheets: 0,
    pendingReadings: 0,
    errorReadings: 0
  });
  const [recentReadings, setRecentReadings] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  // Filter states for data entry dashboard
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    salesRep: '',
    circuit: '',
    tournee: '',
    groupe: '',
    status: '',
    assignedStatus: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // For a real application, you would create a dedicated API endpoint for dashboard stats
        // Here we're simulating by making separate calls

        let dashboardStats = {};

        // Fetch readings data
        const readingsRes = await api.get('/api/readings');
        const readings = readingsRes.data.data || [];
        dashboardStats.pendingReadings = readings.filter(r => r.status === 'pending').length;
        dashboardStats.errorReadings = readings.filter(r => r.status === 'error').length;

        // Fetch reading sheets data
        const sheetsRes = await api.get('/api/reading-sheets');
        const sheets = sheetsRes.data.data || [];
        dashboardStats.totalSheets = sheets.length;

        if (isAdmin) {
          // Admin specific stats
          dashboardStats.assignedSheets = sheets.filter(s => s.status === 'assigned').length;
          dashboardStats.completedSheets = sheets.filter(s => s.status === 'completed').length;

          // Fetch users data for admin
          const usersRes = await api.get('/api/users');
          const users = usersRes.data.data || [];
          dashboardStats.totalUsers = users.length;
          dashboardStats.activeUsers = users.filter(u => u.active).length;

          // Set recent users for admin
          setRecentUsers(users.slice(0, 5).map(user => ({
            id: user._id,
            name: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim(),
            email: user.email,
            role: user.role,
            active: user.active,
            lastLogin: user.lastLogin ? new Date(user.lastLogin).toISOString().split('T')[0] : 'N/A'
          })));

          // Set recent sheets for admin
          setRecentReadings(sheets.slice(0, 5).map(sheet => ({
            id: sheet._id,
            sheetNumber: sheet.sheetNumber || `RS-${sheet._id.substring(0, 6)}`,
            area: sheet.area || 'Zone inconnue',
            assignedTo: sheet.assignedTo?.name || 'Non assignée',
            status: sheet.status || 'pending',
            date: new Date(sheet.createdAt).toISOString().split('T')[0],
            meterCount: sheet.meterCount || 0,
            completedCount: sheet.completedCount || 0
          })));
        } else {
          // Data Entry specific stats - show all sheets for data entry staff
          dashboardStats.totalSheets = sheets.length;
          dashboardStats.assignedSheets = sheets.filter(s => s.status === 'assigned').length;
          dashboardStats.completedSheets = sheets.filter(s => s.status === 'completed').length;

          // Set recent readings for data entry - show all sheets with better formatting
          const formattedSheets = sheets.map(sheet => ({
            id: sheet._id,
            sheetNumber: sheet.sheetNumber || `RS-${sheet._id.substring(0, 6)}`,
            area: sheet.area || 'Zone inconnue',
            assignedTo: sheet.assignedTo ?
              `${sheet.assignedTo.firstName || ''} ${sheet.assignedTo.lastName || ''}`.trim() ||
              sheet.assignedTo.reference || 'Non assignée' : 'Non assignée',
            status: sheet.status || 'unassigned',
            date: new Date(sheet.createdAt).toISOString().split('T')[0],
            assignedDate: sheet.assignedDate ? new Date(sheet.assignedDate).toISOString().split('T')[0] : null,
            completedDate: sheet.completedDate ? new Date(sheet.completedDate).toISOString().split('T')[0] : null,
            circuit: sheet.circuit ? {
              groupe: sheet.circuit.groupe,
              tournee: sheet.circuit.tournee,
              circuit_number: sheet.circuit.circuit_number,
              direction: sheet.circuit.direction,
              agence: sheet.circuit.agence
            } : null,
            meterCount: sheet.meterCount || 0,
            completedCount: sheet.completedCount || 0
          }));

          setRecentReadings(formattedSheets.slice(0, 10)); // Show more sheets for data entry
        }

        setStats({
          ...stats,
          ...dashboardStats
        });
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setLoading(false);

        // Set mock data for development
        if (recentReadings.length === 0) {
          setRecentReadings([
            {
              id: '1',
              sheetNumber: 'RS-2023-001',
              area: 'Zone Résidentielle 1',
              assignedTo: 'John Doe',
              status: 'completed',
              date: '2023-05-10',
              meterCount: 45,
              completedCount: 45
            },
            {
              id: '2',
              sheetNumber: 'RS-2023-002',
              area: 'District Commercial',
              assignedTo: 'Jane Smith',
              status: 'in-progress',
              date: '2023-05-12',
              meterCount: 32,
              completedCount: 18
            },
            {
              id: '3',
              sheetNumber: 'RS-2023-003',
              area: 'Zone Industrielle',
              assignedTo: 'Robert Johnson',
              status: 'pending',
              date: '2023-05-15',
              meterCount: 28,
              completedCount: 0
            }
          ]);
        }

        if (isAdmin && recentUsers.length === 0) {
          setRecentUsers([
            {
              id: '1',
              name: 'John Doe',
              email: '<EMAIL>',
              role: 'admin',
              active: true,
              lastLogin: '2023-05-15'
            },
            {
              id: '2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              role: 'dataEntry',
              active: true,
              lastLogin: '2023-05-14'
            },
            {
              id: '3',
              name: 'Robert Johnson',
              email: '<EMAIL>',
              role: 'dataEntry',
              active: false,
              lastLogin: '2023-04-20'
            }
          ]);
        }
      }
    };

    fetchDashboardData();
  }, [user, isAdmin]);

  // Filter function for data entry dashboard
  const applyFilters = (sheets) => {
    let filtered = sheets;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(sheet =>
        sheet.sheetNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sheet.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sheet.assignedTo.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date filters
    if (filters.dateFrom) {
      filtered = filtered.filter(sheet => sheet.date >= filters.dateFrom);
    }
    if (filters.dateTo) {
      filtered = filtered.filter(sheet => sheet.date <= filters.dateTo);
    }

    // Status filters
    if (filters.status) {
      filtered = filtered.filter(sheet => sheet.status === filters.status);
    }

    // Circuit filters
    if (filters.groupe && sheet.circuit) {
      filtered = filtered.filter(sheet => sheet.circuit?.groupe === filters.groupe);
    }
    if (filters.tournee && sheet.circuit) {
      filtered = filtered.filter(sheet => sheet.circuit?.tournee === filters.tournee);
    }
    if (filters.circuit && sheet.circuit) {
      filtered = filtered.filter(sheet => sheet.circuit?.circuit_number === filters.circuit);
    }

    // Sales rep filter
    if (filters.salesRep) {
      filtered = filtered.filter(sheet =>
        sheet.assignedTo.toLowerCase().includes(filters.salesRep.toLowerCase())
      );
    }

    return filtered;
  };

  // Get filtered readings for display
  const filteredReadings = !isAdmin ? applyFilters(recentReadings) : recentReadings;

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'completed':
        return 'badge-sonelgaz-success';
      case 'in-progress':
      case 'assigned':
        return 'badge-sonelgaz-primary';
      case 'pending':
        return 'badge-sonelgaz-warning';
      case 'error':
        return 'badge-sonelgaz-danger';
      default:
        return 'badge-sonelgaz-secondary';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Complété';
      case 'in-progress':
        return 'En cours';
      case 'assigned':
        return 'Assigné';
      case 'pending':
        return 'En attente';
      case 'error':
        return 'Erreur';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getProgressPercentage = (completed, total) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="space-y-4">
        {/* Page Header - Formal Style */}
        <div className="bg-white p-5 border-b border-gray-200 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-xl font-medium text-gray-800">Tableau de Bord</h2>
              <p className="text-sm text-gray-500 mt-1">
                Bienvenue, {user?.name || user?.firstName || 'Utilisateur'}!
                {isAdmin
                  ? ' Voici l\'état actuel du système.'
                  : ' Voici l\'état actuel de vos relevés de compteurs.'}
              </p>
            </div>
            <div className="mt-3 md:mt-0">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200"
              >
                <FiRefreshCw className="mr-1.5 h-4 w-4" />
                Actualiser
              </button>
            </div>
          </div>
        </div>

        {/* Statistics Cards - Formal Style */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {isAdmin ? (
            // Admin Stats Cards
            <>
              {/* Total Users */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiUsers className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Total Utilisateurs</p>
                    <p className="text-lg font-medium text-gray-800">{stats.totalUsers}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {stats.activeUsers} utilisateurs actifs
                </div>
              </div>

              {/* Total Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiFileText className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Totales</p>
                    <p className="text-lg font-medium text-gray-800">{stats.totalSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Fiches de relevé dans le système
                </div>
              </div>

              {/* Assigned Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiUserCheck className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Assignées</p>
                    <p className="text-lg font-medium text-gray-800">{stats.assignedSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {stats.totalSheets > 0
                    ? `${Math.round((stats.assignedSheets / stats.totalSheets) * 100)}% des fiches assignées`
                    : 'Aucune fiche dans le système'}
                </div>
              </div>

              {/* Completed Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiCheckCircle className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Complétées</p>
                    <p className="text-lg font-medium text-gray-800">{stats.completedSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {stats.totalSheets > 0
                    ? `${Math.round((stats.completedSheets / stats.totalSheets) * 100)}% des fiches complétées`
                    : 'Aucune fiche dans le système'}
                </div>
              </div>
            </>
          ) : (
            // Data Entry Stats Cards
            <>
              {/* Total Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiFileText className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Totales</p>
                    <p className="text-lg font-medium text-gray-800">{stats.totalSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Toutes les fiches dans le système
                </div>
              </div>

              {/* Assigned Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiUserCheck className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Assignées</p>
                    <p className="text-lg font-medium text-gray-800">{stats.assignedSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {stats.totalSheets > 0
                    ? `${Math.round((stats.assignedSheets / stats.totalSheets) * 100)}% des fiches assignées`
                    : 'Aucune fiche dans le système'}
                </div>
              </div>

              {/* Completed Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiCheckCircle className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Complétées</p>
                    <p className="text-lg font-medium text-gray-800">{stats.completedSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {stats.totalSheets > 0
                    ? `${Math.round((stats.completedSheets / stats.totalSheets) * 100)}% des fiches complétées`
                    : 'Aucune fiche dans le système'}
                </div>
              </div>

              {/* Unassigned Sheets */}
              <div className="bg-white p-5 border border-gray-200 shadow-sm">
                <div className="flex items-center">
                  <div className="p-3 bg-gray-100 mr-3 rounded">
                    <FiClock className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Fiches Non Assignées</p>
                    <p className="text-lg font-medium text-gray-800">{stats.totalSheets - stats.assignedSheets - stats.completedSheets}</p>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Fiches en attente d'assignation
                </div>
              </div>
            </>
          )}
        </div>

        {/* Recent Reading Sheets */}
        <div className="bg-white border border-gray-200 shadow-sm">
          <div className="p-5 border-b border-gray-200">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-800">Fiches de Relevé Récentes</h3>
                <p className="text-sm text-gray-500 mt-1">Les dernières fiches de relevé dans le système</p>
              </div>
              <div className="mt-3 md:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Rechercher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="border border-gray-300 rounded px-3 py-1.5 pl-9 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm w-full sm:w-auto"
                  />
                  <FiSearch className="absolute left-3 top-2 text-gray-400" />
                </div>
                {!isAdmin && (
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="inline-flex items-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200"
                  >
                    <FiFilter className="mr-1.5 h-4 w-4" />
                    Filtrer
                    {showFilters && <span className="ml-1 text-xs">(Actif)</span>}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Filter Panel for Data Entry Staff */}
          {!isAdmin && showFilters && (
            <div className="p-5 border-b border-gray-200 bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Date de début</label>
                  <input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Date de fin</label>
                  <input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Statut</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters({...filters, status: e.target.value})}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                  >
                    <option value="">Tous les statuts</option>
                    <option value="unassigned">Non assignée</option>
                    <option value="assigned">Assignée</option>
                    <option value="completed">Complétée</option>
                    <option value="archived">Archivée</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Agent</label>
                  <input
                    type="text"
                    placeholder="Nom de l'agent"
                    value={filters.salesRep}
                    onChange={(e) => setFilters({...filters, salesRep: e.target.value})}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
              </div>
              <div className="mt-3 flex justify-end space-x-2">
                <button
                  onClick={() => setFilters({
                    dateFrom: '', dateTo: '', salesRep: '', circuit: '',
                    tournee: '', groupe: '', status: '', assignedStatus: ''
                  })}
                  className="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  Réinitialiser
                </button>
              </div>
            </div>
          )}

          {filteredReadings.length === 0 ? (
            <div className="p-5 text-center text-gray-500">
              Aucune fiche de relevé disponible
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Numéro</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone</th>
                    {!isAdmin && <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Circuit</th>}
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigné à</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Création</th>
                    {!isAdmin && <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Assignation</th>}
                    {!isAdmin && <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Completion</th>}
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredReadings.map((reading) => (
                    <tr key={reading.id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700">{reading.sheetNumber}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{reading.area}</td>
                      {!isAdmin && (
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {reading.circuit ? (
                            <div className="text-xs">
                              <div>G{reading.circuit.groupe} - T{reading.circuit.tournee}</div>
                              <div className="text-gray-400">C{reading.circuit.circuit_number}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400">Non défini</span>
                          )}
                        </td>
                      )}
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{reading.assignedTo}</td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(reading.status)}`}>
                          {getStatusText(reading.status)}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <FiCalendar className="text-gray-400 mr-1.5" />
                          <span>{reading.date}</span>
                        </div>
                      </td>
                      {!isAdmin && (
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {reading.assignedDate ? (
                            <div className="flex items-center">
                              <FiCalendar className="text-gray-400 mr-1.5" />
                              <span>{reading.assignedDate}</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">Non assignée</span>
                          )}
                        </td>
                      )}
                      {!isAdmin && (
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {reading.completedDate ? (
                            <div className="flex items-center">
                              <FiCalendar className="text-gray-400 mr-1.5" />
                              <span>{reading.completedDate}</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">Non complétée</span>
                          )}
                        </td>
                      )}
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-1">
                          <Link
                            to={`/reading-sheets/${reading.id}`}
                            className="text-gray-500 hover:text-primary-600 p-1"
                            title="Voir les détails"
                          >
                            <FiFileText className="h-4 w-4" />
                          </Link>
                          <button
                            className="text-gray-500 hover:text-primary-600 p-1"
                            title="Plus d'options"
                          >
                            <FiMoreVertical className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          <div className="p-5 border-t border-gray-200 text-right">
            <Link
              to={isAdmin ? "/reading-sheets" : "/manage-readings"}
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Voir toutes les fiches de relevé →
            </Link>
          </div>
        </div>

        {/* Recent Users - Admin Only */}
        {isAdmin && (
          <div className="bg-white border border-gray-200 shadow-sm mt-4">
            <div className="p-5 border-b border-gray-200">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-800">Utilisateurs Récents</h3>
                  <p className="text-sm text-gray-500 mt-1">Les derniers utilisateurs enregistrés dans le système</p>
                </div>
                <div className="mt-3 md:mt-0">
                  <Link
                    to="/add-user"
                    className="inline-flex items-center px-3 py-1.5 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                  >
                    <FiUserPlus className="mr-1.5 h-4 w-4" />
                    Nouvel Utilisateur
                  </Link>
                </div>
              </div>
            </div>

            {recentUsers.length === 0 ? (
              <div className="p-5 text-center text-gray-500">
                Aucun utilisateur disponible
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dernière Connexion</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50 transition-colors duration-150">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">{user.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.role === 'admin' ? 'Administrateur' : 'Agent de saisie'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {user.active ? 'Actif' : 'Inactif'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center">
                            <FiCalendar className="text-gray-400 mr-1.5" />
                            <span>{user.lastLogin}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex space-x-1">
                            <Link
                              to={`/users/details/${user.id}`}
                              className="text-gray-500 hover:text-primary-600 p-1"
                              title="Voir les détails"
                            >
                              <FiFileText className="h-4 w-4" />
                            </Link>
                            <Link
                              to={`/users/update/${user.id}`}
                              className="text-gray-500 hover:text-primary-600 p-1"
                              title="Modifier"
                            >
                              <FiMoreVertical className="h-4 w-4" />
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            <div className="p-5 border-t border-gray-200 text-right">
              <Link
                to="/users"
                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                Voir tous les utilisateurs →
              </Link>
            </div>
          </div>
        )}
      </div> {/* End of space-y-4 container */}
    </ModernLayout>
  );
};

export default Dashboard;
